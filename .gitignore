
# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
node_modules/
/.pnp
.pnp.js

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/
pip-log.txt
pip-delete-this-directory.txt
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Testing
/coverage
.nyc_output

# Cache
.turbo
.npm
.eslintcache

# Misc
.DS_Store
*.pem
Thumbs.db

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Local env files
.env*
!.env.example

# Build outputs
out/
build/
dist/

# Plasmo - https://www.plasmo.com
.plasmo

# BPP - http://bpp.browser.market/
keys.json

# TypeScript
.tsbuildinfo
*.tsbuildinfo

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log