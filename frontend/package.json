{"name": "ai-toolbox", "displayName": "Ai toolbox", "version": "0.0.1", "description": "An ai toolbox", "author": "pengcheng", "scripts": {"dev": "plasmo dev", "build": "plasmo build", "package": "plasmo package"}, "dependencies": {"@ai-sdk/azure": "^0.0.15", "@heroicons/react": "^2.1.4", "@mantine/core": "^7.11.1", "@mantine/hooks": "^7.11.1", "@plasmohq/messaging": "^0.7.1", "@plasmohq/storage": "^1.11.0", "ai": "^3.2.34", "clsx": "^2.1.1", "marked": "^13.0.2", "plasmo": "0.90.5", "react": "18.2.0", "react-dom": "18.2.0", "tailwindcss": "3.4.1"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.1.1", "@types/chrome": "0.0.258", "@types/node": "20.11.5", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "postcss": "8.4.33", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "prettier": "3.2.4", "typescript": "5.3.3"}, "manifest": {"host_permissions": ["https://*/*"]}}